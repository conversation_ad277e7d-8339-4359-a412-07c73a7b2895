"""
Optimized Configuration Management for Options Trading Strategy
Handles environment variables, API credentials, and strategy parameters with proper naming conventions
"""

import os
import json
import logging
from dotenv import load_dotenv
from datetime import datetime

class TradingStrategyConfiguration:
    """Configuration manager for the trading strategy with descriptive naming conventions"""

    def __init__(self):
        """Initialize configuration with proper error handling and descriptive variable names"""
        # Load environment variables
        load_dotenv()

        # API Credentials with descriptive names
        self.zerodha_api_key = os.getenv('API_KEY')
        self.zerodha_api_secret = os.getenv('API_SECRET')
        self.zerodha_username = os.getenv('LOGIN_ID')
        self.zerodha_password = os.getenv('PASSWORD')
        self.zerodha_totp_secret = os.getenv('TOTP_KEY')

        # Strategy Parameters with descriptive names and proper snake_case
        self.trading_strategy_parameters = {
            'total_available_capital': 200000,  # Total capital in INR
            'maximum_capital_deployment_ratio': 0.5,  # 50% max deployment
            'nifty_movement_threshold_delta': 10,  # NIFTY movement threshold
            'banknifty_movement_threshold_delta': 15,  # BANKNIFTY movement threshold
            'portfolio_stop_loss_percentage': 0.015,  # 1.5% portfolio stop loss
            'maximum_position_value_limit': 10000,  # Max per position in INR
            'enable_live_order_execution': False  # Set to True for live trading
        }

        # File paths with descriptive names
        self.excel_dashboard_file_name = 'QuantStrategy_Dashboard.xlsx'
        self.access_token_file_name = f"{self.zerodha_api_key}.json"

        # Validate configuration on initialization
        self._validate_configuration_parameters()
    
    def _validate_configuration_parameters(self):
        """Validate required configuration parameters with descriptive error handling"""
        required_environment_variables = ['API_KEY', 'API_SECRET', 'LOGIN_ID', 'PASSWORD', 'TOTP_KEY']
        missing_environment_variables = []

        for environment_variable_name in required_environment_variables:
            if not os.getenv(environment_variable_name):
                missing_environment_variables.append(environment_variable_name)

        if missing_environment_variables:
            error_message = f"Missing environment variables: {', '.join(missing_environment_variables)}"
            logging.error(f"❌ Configuration validation failed: {error_message}")
            raise ValueError(error_message)

        logging.info("✅ Configuration validated successfully")

    def load_access_token_from_file(self):
        """Load access token from JSON file with improved error handling"""
        try:
            with open(self.access_token_file_name, 'r') as access_token_file:
                access_token_data = json.load(access_token_file)
                return access_token_data.get('access_token')
        except FileNotFoundError:
            logging.error(f"Access token file not found: {self.access_token_file_name}")
            return None
        except json.JSONDecodeError as json_decode_error:
            logging.error(f"Invalid JSON in access token file: {json_decode_error}")
            return None
        except Exception as general_error:
            logging.error(f"Error loading access token from {self.access_token_file_name}: {general_error}")
            return None

    def save_access_token_to_file(self, access_token_data):
        """Save access token data to JSON file with descriptive variable names"""
        try:
            access_token_data['login_timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            with open(self.access_token_file_name, 'w') as access_token_file:
                json.dump(access_token_data, access_token_file, indent=4, default=str)
            logging.info(f"✅ Access token saved to {self.access_token_file_name}")
        except Exception as save_error:
            logging.error(f"Error saving access token: {save_error}")

    def get_strategy_parameter_value(self, parameter_name):
        """Get strategy parameter value with descriptive method name"""
        return self.trading_strategy_parameters.get(parameter_name)

    def update_strategy_parameter_value(self, parameter_name, new_parameter_value):
        """Update strategy parameter value with descriptive method name"""
        self.trading_strategy_parameters[parameter_name] = new_parameter_value
        logging.info(f"📊 Strategy parameter updated: {parameter_name} = {new_parameter_value}")
    
    def get_instrument_freeze_quantity_limits(self):
        """Get freeze quantity limits for different instruments with descriptive naming"""
        return {
            'NIFTY': 1800,  # Maximum lots before freeze
            'BANKNIFTY': 900,  # Maximum lots before freeze
            'DEFAULT': 500  # Default maximum lots before freeze
        }

    def get_instrument_lot_sizes(self):
        """Get standard lot sizes (fallback values) with descriptive naming"""
        return {
            'NIFTY': 50,  # Standard NIFTY lot size
            'BANKNIFTY': 25  # Standard BANKNIFTY lot size
        }

    def get_instrument_strike_price_intervals(self):
        """Get strike price intervals for different instruments with descriptive naming"""
        return {
            'NIFTY': 50,  # NIFTY strike interval in points
            'BANKNIFTY': 100  # BANKNIFTY strike interval in points
        }

    def get_configuration_summary_string(self):
        """Get string representation of configuration with descriptive method name"""
        return f"""
Trading Strategy Configuration Summary:
- API Key: {self.zerodha_api_key[:10]}...{self.zerodha_api_key[-4:]}
- Username: {self.zerodha_username}
- Total Capital: ₹{self.trading_strategy_parameters['total_available_capital']:,}
- Max Deployment: {self.trading_strategy_parameters['maximum_capital_deployment_ratio']*100}%
- NIFTY Delta: {self.trading_strategy_parameters['nifty_movement_threshold_delta']}
- BANKNIFTY Delta: {self.trading_strategy_parameters['banknifty_movement_threshold_delta']}
- Excel File: {self.excel_dashboard_file_name}
- Live Trading: {'Enabled' if self.trading_strategy_parameters['enable_live_order_execution'] else 'Disabled'}
"""

    def __str__(self):
        """String representation of configuration"""
        return self.get_configuration_summary_string()

# Global configuration instance with descriptive name
trading_strategy_config = TradingStrategyConfiguration()
