# Optimized Requirements for Options Trading Strategy Application
# Core trading and market data dependencies
kiteconnect>=4.0.0          # Zerodha KiteConnect API for trading operations
xlwings>=0.30.0             # Excel integration for live dashboard
python-dotenv>=0.19.0       # Environment variable management
pyotp>=2.6.0                # TOTP authentication for Zerodha
requests>=2.28.0            # HTTP requests for API calls
brotli>=1.0.0               # Compression handling for API responses

# Data processing dependencies
pandas>=1.3.0               # Data manipulation and analysis

# Optional web automation (if needed for alternative login methods)
selenium>=4.0.0             # Web browser automation

# Development and testing dependencies (uncomment if needed for development)
# pytest>=7.0.0             # Testing framework
# black>=23.0.0             # Code formatting
# flake8>=6.0.0             # Code linting
# mypy>=1.0.0               # Type checking
