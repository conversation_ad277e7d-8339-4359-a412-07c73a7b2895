# Options Trading Strategy - Mathematical Formulation & Documentation

## Strategy Overview

This is a momentum-based options trading strategy that capitalizes on directional movements in NIFTY and BANKNIFTY indices using dynamic coefficient management and sophisticated risk controls.

## Core Mathematical Formulation

### 1. Entry Conditions

#### Initial Setup (Market Open - 9:15 AM)
```
initial_ltp = underlying_price_at_915
ce_coefficient = 1  # Initial coefficient
```

#### CALL Entry Conditions
```
BANKNIFTY: current_price - initial_ltp >= ce_coefficient × 15
NIFTY: current_price - initial_ltp >= ce_coefficient × 10

Where:
- current_price = real-time underlying price
- initial_ltp = reference price set at market open
- ce_coefficient = dynamic multiplier (starts at 1)
```

#### PUT Entry Conditions (Reverse Logic)
```
BANKNIFTY: initial_ltp - current_price >= ce_coefficient × 15
NIFTY: initial_ltp - current_price >= ce_coefficient × 10
```

#### Option Selection Logic
```
1. Expiry Selection: Next week expiry (never same-day)
2. Strike Selection: ATM (At The Money) or ITM (In The Money)
3. ATM Strike = closest_strike_to_current_price
4. ITM Strike = ATM_strike ± strike_interval (based on direction)
```

### 2. Exit Conditions

#### Fixed Profit/Loss Targets
```
BANKNIFTY Options:
- Take Profit (TP) = entry_price + 20 points
- Stop Loss (SL) = entry_price - 10 points

NIFTY Options:
- Take Profit (TP) = entry_price + 10 points  
- Stop Loss (SL) = entry_price - 5 points
```

#### Trailing Stop Loss
```
Trailing Distance = 2 points (both NIFTY and BANKNIFTY)

If option_price > highest_price_since_entry:
    highest_price_since_entry = option_price
    
trailing_stop = max(stop_loss, highest_price_since_entry - 2)
```

### 3. Dynamic Coefficient Management

#### Coefficient Reset Logic
```
On Take Profit Hit:
    ce_coefficient = 1
    initial_ltp = current_underlying_price
    
On Stop Loss Hit:
    ce_coefficient += 1
    # initial_ltp remains unchanged
```

### 4. 10-Partition Dynamic Trailing System

#### Partition Calculation
```
profit_range = take_profit - stop_loss
partition_size = profit_range / 10
current_partition = floor((current_price - stop_loss) / partition_size)

Dynamic Coefficient Calculation:
dynamic_coeff = 1 + (current_partition / 10) × coefficient_multiplier

Where:
- current_partition ∈ [0, 10]
- coefficient_multiplier = user-defined parameter
```

#### Weighted Proximity Formula
```
proximity_to_tp = (current_price - stop_loss) / (take_profit - stop_loss)
proximity_weight = sigmoid(proximity_to_tp × 6 - 3)  # S-curve weighting

adjusted_trailing = base_trailing × (1 + proximity_weight × adjustment_factor)
```

### 5. Portfolio Management Mathematics

#### Capital Allocation
```
Total Capital = ₹200,000
Maximum Deployment = 50% = ₹100,000
Reserve Capital = 50% = ₹100,000 (always unused)

Per Trade Allocation:
max_position_size = min(available_capital / max_concurrent_trades, max_single_position)
```

#### Portfolio-Level Risk Controls
```
Portfolio Stop Loss Trigger:
total_pnl_percentage = (current_portfolio_value - initial_capital) / initial_capital
if total_pnl_percentage <= -1%:
    exit_all_positions()

Portfolio Take Profit Trigger:
if total_pnl_percentage >= 1.5%:
    enable_portfolio_trailing()

Portfolio Trailing Logic:
if total_pnl_percentage > 2.5%:
    trailing_intervals = floor((total_pnl_percentage - 2.5) / 1.5)
    portfolio_trailing_level = 1.7% + (trailing_intervals × 1.5%)
```

## Strategy Flow Diagram

```
Market Open (9:15 AM)
    ↓
Set initial_ltp, ce_coefficient = 1
    ↓
Monitor Price Movement
    ↓
Entry Condition Met? → No → Continue Monitoring
    ↓ Yes
Select Option (Next Week, ATM/ITM)
    ↓
Enter Position
    ↓
Monitor Exit Conditions
    ↓
Exit Triggered? → No → Update Trailing Stop
    ↓ Yes
Take Profit? → Yes → Reset ce_coefficient = 1, Update initial_ltp
    ↓ No
Stop Loss? → Yes → Increment ce_coefficient += 1
    ↓
Check Portfolio Limits
    ↓
Continue or Exit All
```

## Risk Management Framework

### Position-Level Risk
1. **Fixed Stop Loss**: Predetermined loss limits per option type
2. **Trailing Stop**: Dynamic profit protection
3. **Time Decay**: Monitor theta impact on positions
4. **Volatility Risk**: Consider IV changes

### Portfolio-Level Risk
1. **Capital Allocation**: Maximum 50% deployment
2. **Correlation Risk**: Monitor NIFTY-BANKNIFTY correlation
3. **Drawdown Control**: Portfolio-level stop loss
4. **Profit Protection**: Dynamic trailing at portfolio level

### Market Risk
1. **Gap Risk**: Overnight gap protection
2. **Liquidity Risk**: Ensure adequate option liquidity
3. **Volatility Risk**: Monitor VIX levels
4. **Systemic Risk**: Market-wide event protection

## Performance Metrics

### Trade-Level Metrics
```
Win Rate = winning_trades / total_trades
Average Win = sum(winning_pnl) / winning_trades
Average Loss = sum(losing_pnl) / losing_trades
Profit Factor = gross_profit / gross_loss
```

### Portfolio-Level Metrics
```
Total Return = (final_value - initial_value) / initial_value
Sharpe Ratio = (portfolio_return - risk_free_rate) / portfolio_volatility
Maximum Drawdown = max((peak_value - trough_value) / peak_value)
Calmar Ratio = annual_return / maximum_drawdown
```

### Risk-Adjusted Metrics
```
Sortino Ratio = (portfolio_return - risk_free_rate) / downside_deviation
Information Ratio = active_return / tracking_error
VaR (95%) = portfolio value at 5th percentile of return distribution
```

## Implementation Considerations

### Data Requirements
1. **1-minute OHLCV data** for options
2. **Underlying price data** for NIFTY/BANKNIFTY
3. **Options chain data** for strike selection
4. **Volatility data** for risk assessment

### Technical Requirements
1. **Real-time data processing** capability
2. **Low-latency order execution** system
3. **Risk monitoring** infrastructure
4. **Backup and failover** mechanisms

### Regulatory Compliance
1. **Position limits** adherence
2. **Margin requirements** monitoring
3. **Reporting obligations** fulfillment
4. **Risk disclosure** compliance

This mathematical framework provides the foundation for implementing a robust, systematic options trading strategy with comprehensive risk management and performance optimization capabilities.
