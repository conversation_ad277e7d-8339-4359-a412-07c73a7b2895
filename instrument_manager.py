"""
Optimized Instrument Manager for Dynamic Parameter Handling
Manages instrument data, lot sizes, freeze quantities, and option chains with proper naming conventions
"""

import logging
from datetime import datetime, timedelta
from config import trading_strategy_config

class TradingInstrumentManager:
    """Manage instrument data, lot sizes, freeze quantities, and order limits with descriptive naming"""

    def __init__(self, kite_connect_instance):
        """Initialize instrument manager with descriptive variable names"""
        self.kite_connect_instance = kite_connect_instance
        self.complete_instrument_data_dictionary = {}
        self.underlying_option_instruments_dictionary = {'NIFTY': [], 'BANKNIFTY': []}
        self.nifty_indices_instruments_list = []
        self.option_token_to_symbol_mapping = {}
        self.load_all_instrument_data_from_kite()
    
    def load_all_instrument_data_from_kite(self):
        """Load and process instrument data from KiteConnect with descriptive processing"""
        try:
            logging.info("📊 Loading instrument data from KiteConnect...")
            all_instruments_from_kite = self.kite_connect_instance.instruments()

            for single_instrument in all_instruments_from_kite:
                trading_symbol = single_instrument['tradingsymbol']

                # Store complete instrument data with descriptive structure
                self.complete_instrument_data_dictionary[trading_symbol] = {
                    'instrument_token': single_instrument['instrument_token'],
                    'exchange_token': single_instrument['exchange_token'],
                    'trading_symbol': trading_symbol,
                    'instrument_name': single_instrument['name'],
                    'last_traded_price': single_instrument.get('last_price', 0),
                    'expiry_date': single_instrument.get('expiry', ''),
                    'strike_price': single_instrument.get('strike', 0),
                    'minimum_tick_size': single_instrument.get('tick_size', 0.05),
                    'lot_size': single_instrument.get('lot_size', 1),
                    'instrument_type': single_instrument.get('instrument_type', ''),
                    'market_segment': single_instrument['segment'],
                    'exchange_name': single_instrument['exchange']
                }

                # Categorize instruments with descriptive logic
                if single_instrument['segment'] == 'INDICES':
                    if single_instrument['tradingsymbol'] in ['NIFTY 50', 'NIFTY BANK']:
                        self.nifty_indices_instruments_list.append(single_instrument)

                elif single_instrument['segment'] == 'NFO':
                    # Option instruments categorization
                    if 'NIFTY' in single_instrument['name'] and 'BANK' not in single_instrument['name']:
                        self.underlying_option_instruments_dictionary['NIFTY'].append(single_instrument)
                    elif 'BANKNIFTY' in single_instrument['name']:
                        self.underlying_option_instruments_dictionary['BANKNIFTY'].append(single_instrument)

            logging.info(f"📊 Loaded {len(self.complete_instrument_data_dictionary)} instruments")

            # Log summary with descriptive variables
            nifty_options_count = len(self.underlying_option_instruments_dictionary['NIFTY'])
            banknifty_options_count = len(self.underlying_option_instruments_dictionary['BANKNIFTY'])
            indices_instruments_count = len(self.nifty_indices_instruments_list)

            logging.info(f"📈 Indices: {indices_instruments_count} | NIFTY Options: {nifty_options_count} | BANKNIFTY Options: {banknifty_options_count}")

        except Exception as instrument_loading_error:
            logging.error(f"❌ Error loading instrument data: {instrument_loading_error}")
            raise
    
    def get_complete_instrument_details(self, trading_symbol):
        """Get complete instrument details with descriptive method name"""
        return self.complete_instrument_data_dictionary.get(trading_symbol, {})

    def get_instrument_lot_size(self, trading_symbol):
        """Get lot size for instrument with descriptive method name"""
        instrument_details = self.complete_instrument_data_dictionary.get(trading_symbol, {})
        instrument_lot_size = instrument_details.get('lot_size')

        if instrument_lot_size and instrument_lot_size > 0:
            return instrument_lot_size

        # Fallback to config defaults with descriptive variable names
        if 'NIFTY' in trading_symbol and 'BANK' not in trading_symbol:
            return trading_strategy_config.get_instrument_lot_sizes()['NIFTY']
        elif 'BANKNIFTY' in trading_symbol:
            return trading_strategy_config.get_instrument_lot_sizes()['BANKNIFTY']

        return 1  # Default fallback lot size

    def get_instrument_tick_size(self, trading_symbol):
        """Get tick size for instrument with descriptive method name"""
        return self.complete_instrument_data_dictionary.get(trading_symbol, {}).get('minimum_tick_size', 0.05)

    def calculate_instrument_freeze_quantity(self, trading_symbol):
        """Calculate freeze quantity based on instrument type with descriptive method name"""
        instrument_lot_size = self.get_instrument_lot_size(trading_symbol)
        freeze_quantity_limits = trading_strategy_config.get_instrument_freeze_quantity_limits()

        if 'NIFTY' in trading_symbol and 'BANK' not in trading_symbol:
            return freeze_quantity_limits['NIFTY'] * instrument_lot_size
        elif 'BANKNIFTY' in trading_symbol:
            return freeze_quantity_limits['BANKNIFTY'] * instrument_lot_size
        else:
            return freeze_quantity_limits['DEFAULT'] * instrument_lot_size
    
    def calculate_maximum_order_quantity_with_limits(self, trading_symbol, available_capital_amount, option_current_price):
        """Calculate maximum order quantity based on capital and freeze limits with descriptive method name"""
        try:
            instrument_lot_size = self.get_instrument_lot_size(trading_symbol)
            instrument_freeze_quantity = self.calculate_instrument_freeze_quantity(trading_symbol)

            # Calculate quantity based on available capital with descriptive logic
            if option_current_price > 0:
                maximum_quantity_by_capital = int(available_capital_amount / option_current_price)
            else:
                maximum_quantity_by_capital = instrument_lot_size  # Minimum 1 lot

            # Round down to nearest lot with descriptive variable names
            maximum_lots_by_capital = (maximum_quantity_by_capital // instrument_lot_size) * instrument_lot_size

            # Ensure we don't exceed freeze quantity
            final_maximum_quantity = min(maximum_lots_by_capital, instrument_freeze_quantity)

            # Ensure minimum 1 lot
            if final_maximum_quantity < instrument_lot_size:
                final_maximum_quantity = instrument_lot_size

            return final_maximum_quantity, instrument_lot_size, instrument_freeze_quantity

        except Exception as quantity_calculation_error:
            logging.error(f"❌ Error calculating max quantity for {trading_symbol}: {quantity_calculation_error}")
            return instrument_lot_size, instrument_lot_size, instrument_lot_size
    
    def get_option_chain_with_complete_details(self, underlying_asset, current_spot_price, number_of_strikes=10):
        """Get option chain with complete instrument details using descriptive method name"""
        try:
            # Determine strike interval with descriptive variables
            strike_price_intervals = trading_strategy_config.get_instrument_strike_price_intervals()
            strike_price_interval = strike_price_intervals.get(underlying_asset, 50)

            # Calculate ATM (At The Money) strike with descriptive variable name
            at_the_money_strike_price = round(current_spot_price / strike_price_interval) * strike_price_interval

            # Get strikes for CE and PE with descriptive logic
            target_strike_prices_list = []
            for strike_index in range(-number_of_strikes, number_of_strikes + 1):
                target_strike_prices_list.append(at_the_money_strike_price + (strike_index * strike_price_interval))

            # Get next Thursday (weekly expiry) with descriptive variables
            current_date = datetime.now()
            days_until_next_thursday = 3 - current_date.weekday()  # Thursday is 3
            if days_until_next_thursday <= 0:
                days_until_next_thursday += 7
            next_thursday_date = current_date + timedelta(days_until_next_thursday)
            target_expiry_date_string = next_thursday_date.strftime('%Y-%m-%d')

            option_chain_data = {
                'CE': {},
                'PE': {},
                'tokens': [],
                'expiry': target_expiry_date_string,
                'underlying': underlying_asset,
                'atm_strike': at_the_money_strike_price
            }

            # Search for matching options with descriptive processing
            underlying_options_list = self.underlying_option_instruments_dictionary.get(underlying_asset, [])

            for single_option_instrument in underlying_options_list:
                try:
                    if single_option_instrument.get('expiry') == target_expiry_date_string:
                        option_strike_price = float(single_option_instrument.get('strike', 0))
                        if option_strike_price in target_strike_prices_list:
                            option_instrument_type = single_option_instrument.get('instrument_type', '')
                            option_trading_symbol = single_option_instrument['tradingsymbol']

                            if option_instrument_type in ['CE', 'PE']:
                                option_chain_data[option_instrument_type][option_strike_price] = {
                                    'token': single_option_instrument['instrument_token'],
                                    'symbol': option_trading_symbol,
                                    'strike': option_strike_price,
                                    'expiry': single_option_instrument['expiry'],
                                    'lot_size': single_option_instrument.get('lot_size', self.get_instrument_lot_size(option_trading_symbol)),
                                    'tick_size': single_option_instrument.get('tick_size', 0.05),
                                    'ltp': 0,
                                    'volume': 0,
                                    'oi': 0,
                                    'freeze_qty': self.calculate_instrument_freeze_quantity(option_trading_symbol)
                                }
                                option_chain_data['tokens'].append(single_option_instrument['instrument_token'])
                                self.option_token_to_symbol_mapping[single_option_instrument['instrument_token']] = option_trading_symbol

                except (ValueError, KeyError) as option_processing_error:
                    continue

            logging.info(f"📊 Option chain loaded for {underlying_asset}: {len(option_chain_data['CE'])} CE, {len(option_chain_data['PE'])} PE")
            return option_chain_data

        except Exception as option_chain_error:
            logging.error(f"❌ Error getting option chain for {underlying_asset}: {option_chain_error}")
            return {'CE': {}, 'PE': {}, 'tokens': [], 'expiry': '', 'underlying': underlying_asset, 'atm_strike': 0}
    
    def get_nifty_indices_instrument_tokens(self):
        """Get instrument tokens for NIFTY indices with descriptive method name"""
        return [single_instrument['instrument_token'] for single_instrument in self.nifty_indices_instruments_list]

    def get_nifty_indices_token_to_symbol_mapping(self):
        """Get token to symbol mapping for NIFTY indices with descriptive method name"""
        return {
            single_instrument['instrument_token']: single_instrument['tradingsymbol']
            for single_instrument in self.nifty_indices_instruments_list
        }


