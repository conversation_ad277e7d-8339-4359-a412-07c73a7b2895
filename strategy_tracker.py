"""
Optimized Enhanced Options Strategy Tracker
Implements the strategy from strategy_two.md with dynamic coefficient management and proper naming conventions
"""

import logging
import random
from datetime import datetime
from config import trading_strategy_config

class EnhancedOptionsStrategyTracker:
    """Enhanced Options Trading Strategy as per strategy_two.md with descriptive naming conventions"""

    def __init__(self, trading_order_manager, trading_instrument_manager):
        """Initialize strategy tracker with descriptive variable names"""
        self.trading_order_manager = trading_order_manager
        self.trading_instrument_manager = trading_instrument_manager

        # Capital management with descriptive variable names
        self.total_available_capital = trading_strategy_config.get_strategy_parameter_value('total_available_capital')
        self.maximum_capital_deployment_ratio = trading_strategy_config.get_strategy_parameter_value('maximum_capital_deployment_ratio')
        self.currently_deployed_capital = 0
        self.total_portfolio_profit_and_loss = 0

        # Strategy parameters with descriptive names
        self.nifty_movement_threshold_delta = trading_strategy_config.get_strategy_parameter_value('nifty_movement_threshold_delta')
        self.banknifty_movement_threshold_delta = trading_strategy_config.get_strategy_parameter_value('banknifty_movement_threshold_delta')
        self.portfolio_stop_loss_percentage = trading_strategy_config.get_strategy_parameter_value('portfolio_stop_loss_percentage')

        # Initial LTP tracking with descriptive names
        self.nifty_initial_last_traded_price = None
        self.banknifty_initial_last_traded_price = None

        # Dynamic coefficients with descriptive names
        self.nifty_call_option_coefficient = 1
        self.nifty_put_option_coefficient = 1
        self.banknifty_call_option_coefficient = 1
        self.banknifty_put_option_coefficient = 1

        # Position tracking with descriptive names
        self.nifty_call_option_position = None
        self.nifty_put_option_position = None
        self.banknifty_call_option_position = None
        self.banknifty_put_option_position = None

        # PnL tracking with descriptive names
        self.nifty_call_option_profit_and_loss = 0
        self.nifty_put_option_profit_and_loss = 0
        self.banknifty_call_option_profit_and_loss = 0
        self.banknifty_put_option_profit_and_loss = 0

        # Portfolio management with descriptive names
        self.portfolio_stop_loss_triggered = False

        logging.info("🚀 Enhanced Options Strategy Tracker Initialized")
        logging.info(f"📊 Capital: ₹{self.total_available_capital:,} | Max Deployment: {self.maximum_capital_deployment_ratio*100}%")
        logging.info(f"🎯 NIFTY Delta: {self.nifty_movement_threshold_delta} | BANKNIFTY Delta: {self.banknifty_movement_threshold_delta}")
    
    def set_initial_last_traded_price_for_strategy(self, underlying_instrument, current_last_traded_price):
        """Set initial LTP for strategy reference with descriptive method name"""
        if underlying_instrument == "NIFTY" and self.nifty_initial_last_traded_price is None:
            self.nifty_initial_last_traded_price = current_last_traded_price
            logging.info(f"📊 NIFTY Initial LTP set: {current_last_traded_price}")
        elif underlying_instrument == "BANKNIFTY" and self.banknifty_initial_last_traded_price is None:
            self.banknifty_initial_last_traded_price = current_last_traded_price
            logging.info(f"📊 BANKNIFTY Initial LTP set: {current_last_traded_price}")

        # Activate strategy once both initial LTPs are set
        if self.nifty_initial_last_traded_price and self.banknifty_initial_last_traded_price:
            logging.info("🚀 Enhanced Options Strategy ACTIVATED!")

    def check_option_entry_conditions(self, underlying_instrument, current_last_traded_price):
        """Check entry conditions based on coefficient and delta with descriptive method name"""
        try:
            if underlying_instrument == "NIFTY":
                if self.nifty_initial_last_traded_price is None:
                    return None

                initial_last_traded_price = self.nifty_initial_last_traded_price
                call_option_entry_threshold = self.nifty_call_option_coefficient * self.nifty_movement_threshold_delta
                put_option_entry_threshold = self.nifty_put_option_coefficient * self.nifty_movement_threshold_delta

                # Check CE entry (upward movement) with descriptive logic
                if (current_last_traded_price - initial_last_traded_price) >= call_option_entry_threshold and not self.nifty_call_option_position:
                    return {
                        "option_type": "CE",
                        "underlying_instrument": "NIFTY",
                        "trading_signal": "BUY_CALL",
                        "entry_last_traded_price": current_last_traded_price,
                        "coefficient_used": self.nifty_call_option_coefficient,
                        "threshold_met": call_option_entry_threshold
                    }

                # Check PE entry (downward movement) with descriptive logic
                elif (initial_last_traded_price - current_last_traded_price) >= put_option_entry_threshold and not self.nifty_put_option_position:
                    return {
                        "option_type": "PE",
                        "underlying_instrument": "NIFTY",
                        "trading_signal": "BUY_PUT",
                        "entry_last_traded_price": current_last_traded_price,
                        "coefficient_used": self.nifty_put_option_coefficient,
                        "threshold_met": put_option_entry_threshold
                    }

            elif underlying_instrument == "BANKNIFTY":
                if self.banknifty_initial_last_traded_price is None:
                    return None

                initial_last_traded_price = self.banknifty_initial_last_traded_price
                call_option_entry_threshold = self.banknifty_call_option_coefficient * self.banknifty_movement_threshold_delta
                put_option_entry_threshold = self.banknifty_put_option_coefficient * self.banknifty_movement_threshold_delta

                # Check CE entry (upward movement) with descriptive logic
                if (current_last_traded_price - initial_last_traded_price) >= call_option_entry_threshold and not self.banknifty_call_option_position:
                    return {
                        "option_type": "CE",
                        "underlying_instrument": "BANKNIFTY",
                        "trading_signal": "BUY_CALL",
                        "entry_last_traded_price": current_last_traded_price,
                        "coefficient_used": self.banknifty_call_option_coefficient,
                        "threshold_met": call_option_entry_threshold
                    }

                # Check PE entry (downward movement) with descriptive logic
                elif (initial_last_traded_price - current_last_traded_price) >= put_option_entry_threshold and not self.banknifty_put_option_position:
                    return {
                        "option_type": "PE",
                        "underlying_instrument": "BANKNIFTY",
                        "trading_signal": "BUY_PUT",
                        "entry_last_traded_price": current_last_traded_price,
                        "coefficient_used": self.banknifty_put_option_coefficient,
                        "threshold_met": put_option_entry_threshold
                    }

            return None

        except Exception as entry_conditions_error:
            logging.error(f"❌ Error checking entry conditions: {entry_conditions_error}")
            return None
    
    def enter_option_position_with_validation(self, entry_signal_data):
        """Enter position based on entry signal with dynamic parameters and descriptive naming"""
        try:
            underlying_instrument = entry_signal_data["underlying_instrument"]
            option_type = entry_signal_data["option_type"]
            entry_last_traded_price = entry_signal_data["entry_last_traded_price"]

            # Get option chain data with complete instrument details
            complete_option_chain = self.trading_instrument_manager.get_option_chain_with_complete_details(
                underlying_instrument, entry_last_traded_price, 10
            )

            # Find best option to trade (ITM) with descriptive logic
            strike_price_intervals = trading_strategy_config.get_instrument_strike_price_intervals()
            strike_price_interval = strike_price_intervals.get(underlying_instrument, 50)
            at_the_money_strike_price = round(entry_last_traded_price / strike_price_interval) * strike_price_interval

            # Select ITM strike with descriptive logic
            if option_type == "CE":
                target_strike_price = at_the_money_strike_price - strike_price_interval  # ITM Call
            else:
                target_strike_price = at_the_money_strike_price + strike_price_interval  # ITM Put

            # Get option details with complete instrument data
            if target_strike_price in complete_option_chain[option_type]:
                option_contract_details = complete_option_chain[option_type][target_strike_price]
                option_trading_symbol = option_contract_details['symbol']
                option_instrument_token = option_contract_details['token']
                option_lot_size = option_contract_details['lot_size']
                option_tick_size = option_contract_details['tick_size']

                # Get current option price (simulated for now) with descriptive variables
                simulated_option_price = 100 + random.uniform(-20, 20)  # Simulated price
                option_current_price = round(simulated_option_price / option_tick_size) * option_tick_size

            else:
                # Fallback - generate symbol with descriptive logic
                option_trading_symbol = self.generate_option_trading_symbol(
                    underlying_instrument, option_type, entry_last_traded_price
                )
                option_instrument_token = None
                option_lot_size = trading_strategy_config.get_instrument_lot_sizes().get(underlying_instrument, 50)
                option_tick_size = 0.05
                option_current_price = 100  # Simulated price

            # Calculate optimal position size and quantity with descriptive variables
            remaining_available_capital = self.total_available_capital - self.currently_deployed_capital
            maximum_position_value_limit = min(
                trading_strategy_config.get_strategy_parameter_value('maximum_position_value_limit'),
                remaining_available_capital
            )

            # Calculate quantity based on capital and lot size
            maximum_order_quantity, lot_size_verification, freeze_quantity_limit = self.trading_instrument_manager.calculate_maximum_order_quantity_with_limits(
                option_trading_symbol, maximum_position_value_limit, option_current_price
            )

            final_order_quantity = maximum_order_quantity
            actual_position_value = final_order_quantity * option_current_price

            # Place order with descriptive method call
            placed_order_id = self.trading_order_manager.place_market_order_with_validation(
                trading_symbol=option_trading_symbol,
                order_quantity=final_order_quantity,
                transaction_type='BUY',
                product_type='MIS'
            )

            if not placed_order_id:
                logging.error(f"❌ Failed to place order for {option_trading_symbol}")
                return None
            
            # Create position object with complete instrument data and descriptive structure
            complete_position_details = {
                "option_trading_symbol": option_trading_symbol,
                "option_instrument_token": option_instrument_token,
                "entry_price": option_current_price,
                "entry_timestamp": datetime.now(),
                "entry_spot_price": entry_last_traded_price,
                "position_quantity": final_order_quantity,
                "total_position_value": actual_position_value,
                "highest_price_achieved": option_current_price,
                "trailing_stop_level": None,
                "coefficient_used_for_entry": entry_signal_data["coefficient_used"],
                "placed_order_id": placed_order_id,
                "option_strike_price": target_strike_price,
                "option_lot_size": option_lot_size,
                "option_tick_size": option_tick_size,
                "option_instrument_type": option_type,
                "option_expiry_date": complete_option_chain.get('expiry', ''),
                "freeze_quantity_limit": freeze_quantity_limit
            }

            # Store position with descriptive logic
            if underlying_instrument == "NIFTY":
                if option_type == "CE":
                    self.nifty_call_option_position = complete_position_details
                else:
                    self.nifty_put_option_position = complete_position_details
            else:  # BANKNIFTY
                if option_type == "CE":
                    self.banknifty_call_option_position = complete_position_details
                else:
                    self.banknifty_put_option_position = complete_position_details

            # Update deployed capital with descriptive variable
            self.currently_deployed_capital += actual_position_value

            # Comprehensive logging with descriptive information
            logging.info(f"🟢 ENTERED {underlying_instrument} {option_type}: {option_trading_symbol}")
            logging.info(f"💰 Strike: {target_strike_price} | Qty: {final_order_quantity} | Price: ₹{option_current_price}")
            logging.info(f"📊 Lot Size: {option_lot_size} | Tick Size: ₹{option_tick_size}")
            logging.info(f"🔒 Max Quantity: {maximum_order_quantity} | Freeze Limit: {freeze_quantity_limit}")
            logging.info(f"💰 Position Value: ₹{actual_position_value:,} | Deployed: ₹{self.currently_deployed_capital:,}")
            logging.info(f"📅 Expiry: {complete_option_chain.get('expiry', 'N/A')} | Token: {option_instrument_token}")

            return complete_position_details

        except Exception as position_entry_error:
            logging.error(f"❌ Error entering position: {position_entry_error}")
            return None

    def generate_option_trading_symbol(self, underlying_instrument, option_type, current_spot_price):
        """Generate next week option symbol (ATM/ITM) with descriptive method name"""
        try:
            # Get next Thursday (weekly expiry) with descriptive variables
            current_date = datetime.now()
            days_until_next_thursday = 3 - current_date.weekday()  # Thursday is 3
            if days_until_next_thursday <= 0:  # Target day already happened this week
                days_until_next_thursday += 7
            next_thursday_date = current_date + datetime.timedelta(days_until_next_thursday)

            # Format date for option symbol with descriptive variable
            expiry_date_string = next_thursday_date.strftime("%y%m%d")

            # Calculate strike (ATM/ITM) with descriptive logic
            strike_price_intervals = trading_strategy_config.get_instrument_strike_price_intervals()
            strike_price_interval = strike_price_intervals.get(underlying_instrument, 50)
            base_strike_price = round(current_spot_price / strike_price_interval) * strike_price_interval

            # Slightly ITM for better delta with descriptive logic
            if option_type == "CE":
                final_strike_price = base_strike_price - strike_price_interval  # ITM Call
            else:
                final_strike_price = base_strike_price + strike_price_interval  # ITM Put

            generated_option_symbol = f"{underlying_instrument}{expiry_date_string}{option_type}{int(final_strike_price)}"
            return generated_option_symbol

        except Exception as symbol_generation_error:
            logging.error(f"❌ Error generating option symbol: {symbol_generation_error}")
            return f"{underlying_instrument}_OPTION_{option_type}"

    def get_comprehensive_strategy_data_for_display(self, underlying_instrument):
        """Get enhanced strategy data for Excel display with descriptive method name"""
        try:
            if underlying_instrument == "NIFTY":
                active_nifty_position = self.nifty_call_option_position or self.nifty_put_option_position
                return {
                    "initial_last_traded_price": self.nifty_initial_last_traded_price or 0,
                    "call_option_coefficient": self.nifty_call_option_coefficient,
                    "put_option_coefficient": self.nifty_put_option_coefficient,
                    "active_option_symbol": active_nifty_position["option_trading_symbol"] if active_nifty_position else "NO_POSITION",
                    "current_option_ltp": self.get_simulated_option_price_for_testing(active_nifty_position) if active_nifty_position else 0,
                    "call_option_profit_and_loss": self.nifty_call_option_profit_and_loss,
                    "put_option_profit_and_loss": self.nifty_put_option_profit_and_loss,
                    "total_profit_and_loss": self.nifty_call_option_profit_and_loss + self.nifty_put_option_profit_and_loss,
                    "is_position_active": bool(active_nifty_position),  # InTrade status
                    "active_position_type": active_nifty_position["option_instrument_type"] if active_nifty_position else "",  # CE or PE
                    "position_entry_price": active_nifty_position["entry_price"] if active_nifty_position else 0
                }
            elif underlying_instrument == "BANKNIFTY":
                active_banknifty_position = self.banknifty_call_option_position or self.banknifty_put_option_position
                return {
                    "initial_last_traded_price": self.banknifty_initial_last_traded_price or 0,
                    "call_option_coefficient": self.banknifty_call_option_coefficient,
                    "put_option_coefficient": self.banknifty_put_option_coefficient,
                    "active_option_symbol": active_banknifty_position["option_trading_symbol"] if active_banknifty_position else "NO_POSITION",
                    "current_option_ltp": self.get_simulated_option_price_for_testing(active_banknifty_position) if active_banknifty_position else 0,
                    "call_option_profit_and_loss": self.banknifty_call_option_profit_and_loss,
                    "put_option_profit_and_loss": self.banknifty_put_option_profit_and_loss,
                    "total_profit_and_loss": self.banknifty_call_option_profit_and_loss + self.banknifty_put_option_profit_and_loss,
                    "is_position_active": bool(active_banknifty_position),  # InTrade status
                    "active_position_type": active_banknifty_position["option_instrument_type"] if active_banknifty_position else "",  # CE or PE
                    "position_entry_price": active_banknifty_position["entry_price"] if active_banknifty_position else 0
                }
            return {}
        except Exception as strategy_data_error:
            logging.error(f"❌ Error getting strategy data: {strategy_data_error}")
            return {}

    def get_simulated_option_price_for_testing(self, position_details):
        """Get simulated option price for testing with descriptive method name"""
        if not position_details:
            return 0

        # Simple simulation based on entry price with descriptive variables
        base_entry_price = position_details["entry_price"]
        simulated_price_variation = random.uniform(-10, 10)
        return base_entry_price + simulated_price_variation

    def process_market_tick_data_for_strategy(self, underlying_instrument, current_last_traded_price):
        """Process incoming tick data for strategy execution with descriptive method name"""
        try:
            # Set initial LTP if not set
            self.set_initial_last_traded_price_for_strategy(underlying_instrument, current_last_traded_price)

            # Check portfolio stop loss
            if self.portfolio_stop_loss_triggered:
                logging.warning("⚠️ Portfolio SL hit: 0.00% - No new entries")
                return

            # Check for entry conditions
            option_entry_signal = self.check_option_entry_conditions(underlying_instrument, current_last_traded_price)
            if option_entry_signal:
                new_position = self.enter_option_position_with_validation(option_entry_signal)
                if new_position:
                    logging.info(f"🚀 NEW POSITION: {option_entry_signal}")

            # Portfolio level management
            self.manage_comprehensive_portfolio_risk()

        except Exception as tick_processing_error:
            logging.error(f"❌ Error processing tick data: {tick_processing_error}")

    def manage_comprehensive_portfolio_risk(self):
        """Manage portfolio level risk with descriptive method name"""
        try:
            # Calculate total PnL with descriptive variables
            total_portfolio_profit_and_loss = (
                self.nifty_call_option_profit_and_loss +
                self.nifty_put_option_profit_and_loss +
                self.banknifty_call_option_profit_and_loss +
                self.banknifty_put_option_profit_and_loss
            )
            portfolio_pnl_percentage = (
                (total_portfolio_profit_and_loss / self.total_available_capital) * 100
                if self.total_available_capital > 0 else 0
            )

            # Check portfolio stop loss
            if portfolio_pnl_percentage <= -self.portfolio_stop_loss_percentage * 100:
                self.portfolio_stop_loss_triggered = True
                logging.warning(f"🚨 PORTFOLIO STOP LOSS HIT: {portfolio_pnl_percentage:.2f}%")

        except Exception as portfolio_risk_error:
            logging.error(f"❌ Error in portfolio risk management: {portfolio_risk_error}")
