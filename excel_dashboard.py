"""
Optimized Excel Dashboard for Live Trading Data
Handles real-time Excel updates with strategy tracking and proper naming conventions
"""

import logging
import queue
import threading
from datetime import datetime
import xlwings as xw
from config import trading_strategy_config

class OptimizedExcelTradingDashboard:
    """Pure xlwings implementation for live Excel dashboard - Thread-safe with descriptive naming"""

    def __init__(self):
        """Initialize Excel dashboard with descriptive variable names"""
        self.excel_dashboard_file_name = trading_strategy_config.excel_dashboard_file_name
        self.excel_workbook_instance = None
        self.live_data_worksheet = None
        self.strategy_summary_worksheet = None
        self.configuration_worksheet = None
        self.current_data_row_number = 2  # Start after headers
        self.maximum_rows_limit = 1000  # Limit to prevent Excel from becoming too large
        self.excel_application_instance = None

        # Thread-safe queue for updates with descriptive name
        self.excel_update_queue = queue.Queue()

        # Initialize Excel in main thread only
        self.setup_comprehensive_excel_dashboard()
    
    def setup_comprehensive_excel_dashboard(self):
        """Setup Excel dashboard with all sheets using descriptive method name"""
        try:
            logging.info("🔄 Killing all Excel processes...")
            self.terminate_all_excel_processes()

            logging.info("🚀 Starting Excel with single file policy...")

            # Create xlwings app with specific settings and descriptive variable
            self.excel_application_instance = xw.App(visible=True, add_book=False)

            # Check if file exists with descriptive logic
            import os
            excel_file_exists = os.path.exists(self.excel_dashboard_file_name)
            logging.info(f"📁 File exists: {excel_file_exists}")

            if excel_file_exists:
                # Open existing file with descriptive processing
                self.excel_workbook_instance = self.excel_application_instance.books.open(self.excel_dashboard_file_name)
                logging.info(f"📂 Opened existing file: {self.excel_dashboard_file_name}")

                # Get existing sheets with descriptive variables
                existing_sheet_names = [single_sheet.name for single_sheet in self.excel_workbook_instance.sheets]
                logging.info(f"📋 Found sheets: {existing_sheet_names}")

                # Check if all required sheets exist with descriptive logic
                required_worksheet_names = ['Live_Data', 'Summary', 'Strategy_Config']
                if all(sheet_name in existing_sheet_names for sheet_name in required_worksheet_names):
                    self.live_data_worksheet = self.excel_workbook_instance.sheets['Live_Data']
                    self.strategy_summary_worksheet = self.excel_workbook_instance.sheets['Summary']
                    self.configuration_worksheet = self.excel_workbook_instance.sheets['Strategy_Config']
                    logging.info("✅ All sheets found - using existing setup")
                else:
                    # Create missing sheets
                    self.create_missing_worksheets(existing_sheet_names)
            else:
                # Create new file with descriptive processing
                self.excel_workbook_instance = self.excel_application_instance.books.add()
                self.excel_workbook_instance.save(self.excel_dashboard_file_name)
                logging.info(f"📄 Created new file: {self.excel_dashboard_file_name}")
                self.setup_all_required_worksheets()

            # Final cleanup and setup with descriptive methods
            self.perform_final_cleanup()
            self.setup_worksheet_headers()

        except Exception as excel_setup_error:
            logging.error(f"❌ Error setting up Excel dashboard: {excel_setup_error}")
            raise
    
    def terminate_all_excel_processes(self):
        """Kill all Excel processes with descriptive method name"""
        try:
            import subprocess
            subprocess.run(['taskkill', '/f', '/im', 'excel.exe'],
                         capture_output=True, text=True)
        except Exception as process_termination_error:
            logging.warning(f"⚠️ Could not terminate Excel processes: {process_termination_error}")

    def create_missing_worksheets(self, existing_worksheet_names):
        """Create missing worksheets with descriptive method name"""
        required_worksheet_names = ['Live_Data', 'Summary', 'Strategy_Config']

        for worksheet_name in required_worksheet_names:
            if worksheet_name not in existing_worksheet_names:
                self.excel_workbook_instance.sheets.add(worksheet_name)
                logging.info(f"📄 Created worksheet: {worksheet_name}")

        # Assign sheet references with descriptive variables
        self.live_data_worksheet = self.excel_workbook_instance.sheets['Live_Data']
        self.strategy_summary_worksheet = self.excel_workbook_instance.sheets['Summary']
        self.configuration_worksheet = self.excel_workbook_instance.sheets['Strategy_Config']

    def setup_all_required_worksheets(self):
        """Setup all worksheets for new file with descriptive method name"""
        # Rename default sheet to Live_Data with descriptive processing
        default_worksheet = self.excel_workbook_instance.sheets[0]
        default_worksheet.name = 'Live_Data'
        self.live_data_worksheet = default_worksheet

        # Create additional sheets with descriptive variables
        self.strategy_summary_worksheet = self.excel_workbook_instance.sheets.add('Summary')
        self.configuration_worksheet = self.excel_workbook_instance.sheets.add('Strategy_Config')

        logging.info("✅ All worksheets created")

    def perform_final_cleanup(self):
        """Ensure only our file is open with descriptive method name"""
        try:
            total_workbooks_count = len(self.excel_application_instance.books)
            logging.info(f"🔍 Total Excel books before cleanup: {total_workbooks_count}")

            if total_workbooks_count == 1:
                logging.info("✅ Perfect! Only 1 Excel book open")
                return

            # Close other books with descriptive processing
            for single_workbook in list(self.excel_application_instance.books):
                if single_workbook != self.excel_workbook_instance:
                    try:
                        single_workbook.close()
                        logging.info(f"✅ Closed: {single_workbook.name}")
                    except Exception as workbook_close_error:
                        logging.warning(f"⚠️ Could not close workbook: {workbook_close_error}")

        except Exception as cleanup_error:
            logging.error(f"❌ Error in cleanup: {cleanup_error}")
    
    def setup_worksheet_headers(self):
        """Setup headers for all worksheets with descriptive method name"""
        try:
            # Live Data headers with descriptive column names
            live_data_column_headers = [
                'Timestamp', 'Instrument', 'Token', 'LTP', 'Volume', 'Change%',
                'Initial LTP', 'CE Coeff', 'PE Coeff', 'Option Symbol', 'Option LTP',
                'CE PnL', 'PE PnL', 'Total PnL', 'Open', 'High', 'Low', 'Close'
            ]

            self.live_data_worksheet.range('A1').value = live_data_column_headers

            # Summary headers with descriptive column names
            strategy_summary_column_headers = ['Metric', 'NIFTY', 'BANKNIFTY', 'Total']
            self.strategy_summary_worksheet.range('A1').value = strategy_summary_column_headers

            # Strategy Config headers with descriptive column names
            configuration_column_headers = ['Parameter', 'NIFTY', 'BANKNIFTY', 'Description']
            self.configuration_worksheet.range('A1').value = configuration_column_headers

            # Auto-fit columns for all worksheets
            self.live_data_worksheet.autofit()
            self.strategy_summary_worksheet.autofit()
            self.configuration_worksheet.autofit()

            # Save workbook
            self.excel_workbook_instance.save()

            logging.info("✅ Headers setup complete")

        except Exception as headers_setup_error:
            logging.error(f"❌ Error setting up headers: {headers_setup_error}")
    
    def queue_market_data_update(self, market_tick_data):
        """Queue update for thread-safe processing with descriptive method name"""
        try:
            self.excel_update_queue.put(market_tick_data, block=False)
        except queue.Full:
            logging.warning("⚠️ Update queue is full, skipping update")

    def process_queued_excel_updates(self):
        """Process queued Excel updates - Main thread only with descriptive method name"""
        try:
            total_updates_processed = 0
            maximum_updates_per_batch = 10

            while not self.excel_update_queue.empty() and total_updates_processed < maximum_updates_per_batch:
                try:
                    queued_tick_data = self.excel_update_queue.get_nowait()
                    self._update_excel_worksheet_directly(queued_tick_data)
                    total_updates_processed += 1
                except queue.Empty:
                    break
                except Exception as update_processing_error:
                    logging.error(f"❌ Error processing update: {update_processing_error}")

        except Exception as excel_updates_error:
            logging.error(f"❌ Error in process_excel_updates: {excel_updates_error}")
    
    def _update_excel_worksheet_directly(self, market_tick_data):
        """Direct Excel update with strategy tracking and descriptive method name"""
        try:
            current_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            instrument_token = market_tick_data['instrument_token']
            last_traded_price = market_tick_data.get('last_price', 0)
            trading_volume = market_tick_data.get('volume', 0)
            price_change = market_tick_data.get('change', 0)
            price_change_percentage = price_change * 100 if price_change else 0

            # Get OHLC data with descriptive variables
            ohlc_data = market_tick_data.get('ohlc', {})
            opening_price = ohlc_data.get('open', 0)
            highest_price = ohlc_data.get('high', 0)
            lowest_price = ohlc_data.get('low', 0)
            closing_price = ohlc_data.get('close', 0)

            # Determine instrument with descriptive logic
            if instrument_token == 256265:
                instrument_name = 'NIFTY 50'
                strategy_underlying_instrument = 'NIFTY'
            elif instrument_token == 260105:
                instrument_name = 'NIFTY BANK'
                strategy_underlying_instrument = 'BANKNIFTY'
            else:
                instrument_name = 'Unknown'
                strategy_underlying_instrument = None

            # Prepare basic row data with descriptive structure
            excel_row_data = [
                current_timestamp, instrument_name, instrument_token, last_traded_price, trading_volume, price_change_percentage,
                0, 1, 1, 'NO_POSITION', 0, 0, 0, 0,  # Strategy columns (placeholder)
                opening_price, highest_price, lowest_price, closing_price
            ]

            # Update Excel with descriptive logic
            if self.current_data_row_number <= self.maximum_rows_limit:
                self.live_data_worksheet.range(f'A{self.current_data_row_number}').value = excel_row_data
                self.current_data_row_number += 1
            else:
                # Reset to avoid Excel becoming too large
                self.current_data_row_number = 2
                logging.info("📊 Excel rows reset to prevent file bloat")

            # Auto-save periodically with descriptive logic
            auto_save_interval = 50
            if self.current_data_row_number % auto_save_interval == 0:
                self.excel_workbook_instance.save()

            logging.info(f"📈 Excel Updated: {instrument_name} - LTP: {last_traded_price} - Change: {price_change_percentage:.2f}%")

        except Exception as excel_update_error:
            logging.error(f"❌ Error updating Excel: {excel_update_error}")
    
    def update_strategy_specific_data_in_excel(self, underlying_instrument, comprehensive_strategy_data):
        """Update strategy-specific data in Excel with descriptive method name"""
        try:
            # This method can be enhanced to update strategy columns
            # with real-time strategy data using descriptive processing
            if comprehensive_strategy_data:
                logging.debug(f"📊 Strategy data update for {underlying_instrument}")
        except Exception as strategy_data_update_error:
            logging.error(f"❌ Error updating strategy data: {strategy_data_update_error}")

    def close_excel_dashboard_safely(self):
        """Close Excel dashboard safely with descriptive method name"""
        try:
            if self.excel_workbook_instance:
                self.excel_workbook_instance.save()
                self.excel_workbook_instance.close()
            if self.excel_application_instance:
                self.excel_application_instance.quit()
            logging.info("✅ Excel dashboard closed")
        except Exception as excel_close_error:
            logging.error(f"❌ Error closing Excel: {excel_close_error}")

    def get_current_data_row_number(self):
        """Get current row number with descriptive method name"""
        return self.current_data_row_number

    def get_excel_update_queue_size(self):
        """Get update queue size with descriptive method name"""
        return self.excel_update_queue.qsize()


