import os
import json
import pyotp
import requests
from dotenv import load_dotenv
from kiteconnect import KiteConnect
import time
from urllib.parse import urlparse, parse_qs
import brotli

############################################################################
# Author: Fixed version handling Brotli compression and proper API calls
# Based on <PERSON><PERSON><PERSON>hand<PERSON>'s approach with compression handling
############################################################################

# Load environment variables from .env file
load_dotenv()

# Retrieve credentials from environment variables
kite_api_key = os.getenv('API_KEY')
kite_api_secret = os.getenv('API_SECRET')
username = os.getenv('LOGIN_ID')  # Zerodha User ID
password = os.getenv('PASSWORD')   # Zerodha Password
totp_secret = os.getenv('TOTP_KEY')  # TOTP Secret Key

# ✅ API URLs based on Rashmin Bhanderi's article
BASE_URL = "https://kite.zerodha.com"
LOGIN_URL = BASE_URL + "/api/login"
TWOFA_URL = BASE_URL + "/api/twofa"

session_file = f"{kite_api_key}.json"

def decompress_response(response):
    """Handle different compression types"""
    content_encoding = response.headers.get('content-encoding', '').lower()
    
    if content_encoding == 'br':
        try:
            return brotli.decompress(response.content).decode('utf-8')
        except Exception as e:
            print(f"Failed to decompress brotli: {e}")
            return response.text
    elif content_encoding == 'gzip':
        return response.text  # requests handles gzip automatically
    else:
        return response.text

def autologin_zerodha():
    """
    Automated login function with proper compression handling
    """
    print("🚀 Starting Zerodha auto-login process...")
    
    # Check for existing session file
    if os.path.isfile(session_file):
        print(f"✅ {session_file} present in current directory")
        with open(session_file, 'r') as f:
            data = json.load(f)
        print(f"Previous login time for {data.get('user_name', 'Unknown')} is {data.get('login_time', 'Unknown')}")
        os.remove(session_file)
        print(f"🗑️ {session_file} has been deleted.")
    else:
        print(f"ℹ️  {session_file} does not exist in current directory")

    try:
        # Create a session with proper headers
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://kite.zerodha.com/',
            'Origin': 'https://kite.zerodha.com'
        })
        
        # Step 1: Initial Login Request
        print("📝 Step 1: Sending initial login request...")
        login_payload = {
            'user_id': username,
            'password': password
        }
        
        response = session.post(LOGIN_URL, data=login_payload)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Login request failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return None, None
        
        # Decompress and parse response
        try:
            response_text = decompress_response(response)
            print(f"Decompressed response: {response_text}")
            
            response_data = json.loads(response_text)
            
            # Check if login was successful
            if response_data.get('status') != 'success':
                print(f"❌ Login failed: {response_data}")
                return None, None
            
            request_id = response_data['data']['request_id']
            print(f"✅ Got request_id: {request_id}")
            
        except (KeyError, json.JSONDecodeError) as e:
            print(f"❌ Failed to parse login response: {e}")
            print(f"Raw response: {response.content}")
            return None, None

        # Step 2: Two-Factor Authentication (2FA)
        print("🔐 Step 2: Handling Two-Factor Authentication...")
        
        # Generate TOTP using the secret key
        twofa_pin = pyotp.TOTP(totp_secret).now()
        print(f"Generated TOTP: {twofa_pin}")
        
        # Send 2FA request
        twofa_payload = {
            'user_id': username,
            'request_id': request_id,
            'twofa_value': twofa_pin,
            'twofa_type': 'totp'
        }
        
        response_2fa = session.post(TWOFA_URL, data=twofa_payload)
        print(f"2FA response status: {response_2fa.status_code}")
        
        if response_2fa.status_code != 200:
            print(f"❌ 2FA request failed with status: {response_2fa.status_code}")
            response_2fa_text = decompress_response(response_2fa)
            print(f"Response: {response_2fa_text}")
            return None, None
        
        # Parse 2FA response
        try:
            response_2fa_text = decompress_response(response_2fa)
            print(f"2FA response: {response_2fa_text}")
            
            response_2fa_data = json.loads(response_2fa_text)
            
            # Check if 2FA was successful
            if response_2fa_data.get('status') != 'success':
                print(f"❌ 2FA failed: {response_2fa_data}")
                return None, None
                
            print("✅ 2FA authentication successful")
            
        except json.JSONDecodeError as e:
            # Sometimes 2FA response might not be JSON if successful
            print(f"2FA response parsing note: {e}")
            print("Continuing assuming 2FA was successful...")

        # Step 3: Initialize KiteConnect and Generate Login URL
        print("🔗 Step 3: Generating Kite Connect login URL...")
        
        kite = KiteConnect(api_key=kite_api_key)
        kite_login_url = kite.login_url()
        print(f"Kite login URL: {kite_login_url}")

        # Step 4: Get the request token using the authenticated session
        print("🎟️ Step 4: Extracting request token...")
        
        # Make the request with the authenticated session
        try:
            response_token = session.get(kite_login_url, allow_redirects=False)
            print(f"Token request status: {response_token.status_code}")
            
            # Handle different response scenarios
            if response_token.status_code in [302, 301, 307, 308]:
                # Redirect case
                redirect_url = response_token.headers.get('Location')
                print(f"Redirect URL: {redirect_url}")
                
                if redirect_url and 'request_token=' in redirect_url:
                    parsed_url = urlparse(redirect_url)
                    query_params = parse_qs(parsed_url.query)
                    request_token = query_params.get('request_token', [None])[0]
                    
                    if request_token:
                        print(f'🎉 Found Request Token from redirect: {request_token}')
                        return generate_access_token(kite, request_token)
                
            elif response_token.status_code == 200:
                # Direct response case
                response_content = decompress_response(response_token)
                print("Response content preview:")
                print(response_content[:500] if len(response_content) > 500 else response_content)
                
                # Look for request_token in various ways
                if 'request_token=' in response_content:
                    # Method 1: Simple string search
                    try:
                        token_start = response_content.find('request_token=') + len('request_token=')
                        token_end = response_content.find('&', token_start)
                        if token_end == -1:
                            token_end = response_content.find('"', token_start)
                        if token_end == -1:
                            token_end = response_content.find("'", token_start)
                        if token_end == -1:
                            # Look for end of line or space
                            token_end = response_content.find('\n', token_start)
                            if token_end == -1:
                                token_end = response_content.find(' ', token_start)
                        
                        if token_end != -1:
                            request_token = response_content[token_start:token_end].strip()
                            print(f'🎉 Found Request Token in response: {request_token}')
                            return generate_access_token(kite, request_token)
                    except Exception as e:
                        print(f"Method 1 failed: {e}")
                
                # Method 2: Look for JavaScript redirect
                import re
                token_patterns = [
                    r'request_token=([a-zA-Z0-9]+)',
                    r'"request_token":"([a-zA-Z0-9]+)"',
                    r"'request_token':'([a-zA-Z0-9]+)'",
                    r'request_token%3D([a-zA-Z0-9]+)'
                ]
                
                for pattern in token_patterns:
                    match = re.search(pattern, response_content)
                    if match:
                        request_token = match.group(1)
                        print(f'🎉 Found Request Token with regex: {request_token}')
                        return generate_access_token(kite, request_token)
            
            # If we reach here, no token was found
            print("❌ No request_token found in response")
            print("Let's try following redirects manually...")
            
            # Try following redirects manually
            response_follow = session.get(kite_login_url, allow_redirects=True)
            final_url = response_follow.url
            print(f"Final URL after following redirects: {final_url}")
            
            if 'request_token=' in final_url:
                parsed_url = urlparse(final_url)
                query_params = parse_qs(parsed_url.query)
                request_token = query_params.get('request_token', [None])[0]
                
                if request_token:
                    print(f'🎉 Found Request Token in final URL: {request_token}')
                    return generate_access_token(kite, request_token)
            
            print("❌ Could not extract request_token")
            return None, None
                
        except Exception as token_error:
            print(f"❌ Error during token extraction: {token_error}")
            import traceback
            traceback.print_exc()
            return None, None

    except requests.exceptions.RequestException as req_error:
        print(f"❌ Network request error: {req_error}")
        return None, None
    except Exception as general_error:
        print(f"❌ General error occurred: {general_error}")
        import traceback
        traceback.print_exc()
        return None, None

def generate_access_token(kite, request_token):
    """Generate access token from request token"""
    try:
        session_data = kite.generate_session(request_token, kite_api_secret)
        access_token = session_data['access_token']
        
        print(f"✅ Access Token Generated: {access_token}")
        print(f"👤 User: {session_data.get('user_name', 'Unknown')}")
        
        # Save session data to file with timestamp
        session_data['login_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=4, default=str)
        
        print(f"💾 Session data saved to {session_file}")
        
        return kite, access_token
        
    except Exception as e:
        print(f"❌ Failed to generate access token: {e}")
        return None, None

def main():
    """Main function to execute the login process"""
    print("=" * 70)
    print("🏦 ZERODHA KITE CONNECT AUTO-LOGIN")
    print("=" * 70)
    
    # Validate environment variables
    required_vars = ['API_KEY', 'API_SECRET', 'LOGIN_ID', 'PASSWORD', 'TOTP_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file")
        return
    
    print(f"📊 API Key: {kite_api_key[:10]}...{kite_api_key[-4:]}")
    print(f"👤 Username: {username}")
    print(f"🔐 TOTP Secret: {'*' * len(totp_secret) if totp_secret else 'Not set'}")
    print("=" * 70)
    
    # Execute auto-login
    kite_instance, access_token = autologin_zerodha()
    
    if kite_instance and access_token:
        print("\n" + "=" * 70)
        print("🎉 LOGIN SUCCESSFUL!")
        print("=" * 70)
        print(f"🔑 Access Token: {access_token[:20]}...{access_token[-10:]}")
        print("✅ You can now use this access token for trading operations")
        print("📁 Session details saved to:", session_file)
        
        # Test the connection
        try:
            kite_instance.set_access_token(access_token)
            profile = kite_instance.profile()
            print(f"👋 Welcome back, {profile.get('user_name', 'Trader')}!")
            print(f"📧 Email: {profile.get('email', 'N/A')}")
            print(f"🏢 Broker: {profile.get('broker', 'N/A')}")
        except Exception as profile_error:
            print(f"⚠️  Could not fetch profile: {profile_error}")
        
    else:
        print("\n" + "=" * 70)
        print("❌ LOGIN FAILED!")
        print("=" * 70)
        print("🔍 Please check:")
        print("  1. Your credentials in the .env file")
        print("  2. Your TOTP secret key")
        print("  3. Your internet connection")
        print("  4. Zerodha server status")

if __name__ == "__main__":
    main()