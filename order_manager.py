"""
Optimized Order Manager for Dynamic Order Placement
Handles order placement, validation, and management using KiteConnect API with proper naming conventions
"""

import logging
from datetime import datetime
from config import trading_strategy_config

class TradingOrderManager:
    """Handle order placement and management using KiteConnect with dynamic parameters and descriptive naming"""

    def __init__(self, kite_connect_instance, trading_instrument_manager):
        """Initialize order manager with descriptive variable names"""
        self.kite_connect_instance = kite_connect_instance
        self.trading_instrument_manager = trading_instrument_manager
        self.active_orders_dictionary = {}
        self.complete_order_history_list = []

        logging.info("📋 TradingOrderManager initialized with dynamic validation")
    
    def place_market_order_with_validation(self, trading_symbol, order_quantity, transaction_type, product_type="MIS"):
        """Place market order using KiteConnect API with dynamic validation and descriptive naming"""
        try:
            # Get instrument details with descriptive variable names
            complete_instrument_details = self.trading_instrument_manager.get_complete_instrument_details(trading_symbol)
            if not complete_instrument_details:
                logging.error(f"❌ Instrument details not found for {trading_symbol}")
                return None

            # Validate lot size with descriptive processing
            instrument_lot_size = complete_instrument_details.get('lot_size', 1)
            original_order_quantity = order_quantity

            if order_quantity % instrument_lot_size != 0:
                order_quantity = (order_quantity // instrument_lot_size) * instrument_lot_size
                if order_quantity == 0:
                    order_quantity = instrument_lot_size
                logging.warning(f"⚠️ Quantity adjusted from {original_order_quantity} to {order_quantity} (Lot size: {instrument_lot_size})")

            # Check freeze quantity with descriptive variables
            instrument_freeze_quantity = self.trading_instrument_manager.calculate_instrument_freeze_quantity(trading_symbol)
            if order_quantity > instrument_freeze_quantity:
                logging.warning(f"⚠️ Quantity {order_quantity} exceeds freeze limit {instrument_freeze_quantity}")
                order_quantity = instrument_freeze_quantity

            # Determine exchange with descriptive variable name
            trading_exchange = complete_instrument_details.get('exchange_name', 'NFO')

            # Prepare order parameters with descriptive structure
            market_order_parameters = {
                'tradingsymbol': trading_symbol,
                'exchange': trading_exchange,
                'transaction_type': transaction_type,  # BUY or SELL
                'quantity': order_quantity,
                'order_type': 'MARKET',
                'product': product_type,  # MIS for intraday
                'validity': 'DAY'
            }

            logging.info(f"📤 Placing {transaction_type} market order: {trading_symbol} x {order_quantity}")
            logging.info(f"📊 Exchange: {trading_exchange} | Lot Size: {instrument_lot_size} | Freeze Limit: {instrument_freeze_quantity}")

            # Place order only if live trading is enabled
            if trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution'):
                order_id = self.kite_connect_instance.place_order(**market_order_parameters)
                logging.info(f"✅ LIVE ORDER PLACED - Order ID: {order_id}")
            else:
                order_id = f"SIM_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{trading_symbol}"
                logging.info(f"🎮 SIMULATED ORDER - Order ID: {order_id}")

            # Store order details with instrument info and descriptive structure
            complete_order_details = {
                'order_id': order_id,
                'trading_symbol': trading_symbol,
                'order_quantity': order_quantity,
                'transaction_type': transaction_type,
                'order_type': 'MARKET',
                'product_type': product_type,
                'trading_exchange': trading_exchange,
                'instrument_lot_size': instrument_lot_size,
                'instrument_freeze_quantity': instrument_freeze_quantity,
                'order_timestamp': datetime.now(),
                'order_status': 'PENDING',
                'is_simulated_order': not trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution')
            }

            self.active_orders_dictionary[order_id] = complete_order_details
            self.complete_order_history_list.append(complete_order_details)

            return order_id

        except Exception as market_order_error:
            logging.error(f"❌ Error placing market order: {market_order_error}")
            return None
    
    def place_limit_order_with_validation(self, trading_symbol, order_quantity, limit_price, transaction_type, product_type="MIS"):
        """Place limit order using KiteConnect API with dynamic validation and descriptive naming"""
        try:
            # Get instrument details with descriptive variable names
            complete_instrument_details = self.trading_instrument_manager.get_complete_instrument_details(trading_symbol)
            if not complete_instrument_details:
                logging.error(f"❌ Instrument details not found for {trading_symbol}")
                return None

            # Validate lot size with descriptive processing
            instrument_lot_size = complete_instrument_details.get('lot_size', 1)
            original_order_quantity = order_quantity

            if order_quantity % instrument_lot_size != 0:
                order_quantity = (order_quantity // instrument_lot_size) * instrument_lot_size
                if order_quantity == 0:
                    order_quantity = instrument_lot_size
                logging.warning(f"⚠️ Quantity adjusted from {original_order_quantity} to {order_quantity}")

            # Check freeze quantity with descriptive variables
            instrument_freeze_quantity = self.trading_instrument_manager.calculate_instrument_freeze_quantity(trading_symbol)
            if order_quantity > instrument_freeze_quantity:
                logging.warning(f"⚠️ Quantity {order_quantity} exceeds freeze limit {instrument_freeze_quantity}")
                order_quantity = instrument_freeze_quantity

            # Validate price with tick size using descriptive variables
            instrument_tick_size = complete_instrument_details.get('minimum_tick_size', 0.05)
            original_limit_price = limit_price
            adjusted_limit_price = round(limit_price / instrument_tick_size) * instrument_tick_size

            if abs(adjusted_limit_price - limit_price) > 0.001:  # Small tolerance for floating point
                logging.info(f"📊 Price adjusted from ₹{original_limit_price} to ₹{adjusted_limit_price} (Tick: ₹{instrument_tick_size})")
                limit_price = adjusted_limit_price

            # Determine exchange with descriptive variable name
            trading_exchange = complete_instrument_details.get('exchange_name', 'NFO')

            # Prepare order parameters with descriptive structure
            limit_order_parameters = {
                'tradingsymbol': trading_symbol,
                'exchange': trading_exchange,
                'transaction_type': transaction_type,
                'quantity': order_quantity,
                'order_type': 'LIMIT',
                'price': limit_price,
                'product': product_type,
                'validity': 'DAY'
            }

            logging.info(f"📤 Placing {transaction_type} limit order: {trading_symbol} x {order_quantity} @ ₹{limit_price}")
            logging.info(f"📊 Lot: {instrument_lot_size} | Tick: ₹{instrument_tick_size} | Freeze: {instrument_freeze_quantity}")

            # Place order only if live trading is enabled
            if trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution'):
                order_id = self.kite_connect_instance.place_order(**limit_order_parameters)
                logging.info(f"✅ LIVE LIMIT ORDER PLACED - Order ID: {order_id}")
            else:
                order_id = f"SIM_LMT_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{trading_symbol}"
                logging.info(f"🎮 SIMULATED LIMIT ORDER - Order ID: {order_id}")

            # Store order details with instrument info and descriptive structure
            complete_order_details = {
                'order_id': order_id,
                'trading_symbol': trading_symbol,
                'order_quantity': order_quantity,
                'transaction_type': transaction_type,
                'order_type': 'LIMIT',
                'limit_price': limit_price,
                'product_type': product_type,
                'trading_exchange': trading_exchange,
                'instrument_lot_size': instrument_lot_size,
                'instrument_tick_size': instrument_tick_size,
                'instrument_freeze_quantity': instrument_freeze_quantity,
                'order_timestamp': datetime.now(),
                'order_status': 'PENDING',
                'is_simulated_order': not trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution')
            }

            self.active_orders_dictionary[order_id] = complete_order_details
            self.complete_order_history_list.append(complete_order_details)

            return order_id

        except Exception as limit_order_error:
            logging.error(f"❌ Error placing limit order: {limit_order_error}")
            return None
    
    def cancel_existing_order(self, order_id):
        """Cancel an existing order with descriptive method name"""
        try:
            if trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution'):
                self.kite_connect_instance.cancel_order(order_id=order_id)
                logging.info(f"🚫 Order cancelled: {order_id}")
            else:
                logging.info(f"🎮 Simulated order cancellation: {order_id}")

            if order_id in self.active_orders_dictionary:
                self.active_orders_dictionary[order_id]['order_status'] = 'CANCELLED'

            return True

        except Exception as order_cancellation_error:
            logging.error(f"❌ Error cancelling order {order_id}: {order_cancellation_error}")
            return False

    def get_specific_order_status(self, order_id):
        """Get status of a specific order with descriptive method name"""
        try:
            if trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution'):
                all_orders_list = self.kite_connect_instance.orders()
                for single_order in all_orders_list:
                    if single_order['order_id'] == order_id:
                        return single_order
            else:
                # Return simulated order status
                return self.active_orders_dictionary.get(order_id)

            return None

        except Exception as order_status_error:
            logging.error(f"❌ Error getting order status: {order_status_error}")
            return None

    def get_current_trading_positions(self):
        """Get current trading positions with descriptive method name"""
        try:
            if trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution'):
                current_positions = self.kite_connect_instance.positions()
                return current_positions
            else:
                # Return simulated positions
                return {'net': [], 'day': []}

        except Exception as positions_error:
            logging.error(f"❌ Error getting positions: {positions_error}")
            return None

    def get_complete_order_history(self):
        """Get complete order history with descriptive method name"""
        return self.complete_order_history_list.copy()

    def get_all_active_orders(self):
        """Get all active orders with descriptive method name"""
        return {
            order_id: order_details
            for order_id, order_details in self.active_orders_dictionary.items()
            if order_details['order_status'] == 'PENDING'
        }

    def update_specific_order_status(self, order_id, new_order_status):
        """Update specific order status with descriptive method name"""
        if order_id in self.active_orders_dictionary:
            self.active_orders_dictionary[order_id]['order_status'] = new_order_status
            logging.info(f"📋 Order {order_id} status updated to {new_order_status}")

    def get_comprehensive_order_summary(self):
        """Get comprehensive summary of all orders with descriptive method name"""
        total_orders_count = len(self.complete_order_history_list)
        active_orders_count = len(self.get_all_active_orders())

        return {
            'total_orders_count': total_orders_count,
            'active_orders_count': active_orders_count,
            'completed_orders_count': total_orders_count - active_orders_count,
            'live_trading_enabled': trading_strategy_config.get_strategy_parameter_value('enable_live_order_execution')
        }
