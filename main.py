###############################################################################
#
# Real-time CRUDE OIL and NATURAL GAS LTP Tracker with Excel Integration
# Live market data streaming using KiteConnect WebSocket API with xlwings Excel updates
# Based on official KiteTicker threaded mode example
#
###############################################################################

import os
import time
import logging
import threading
import json
import queue
from datetime import datetime
from dotenv import load_dotenv
from kiteconnect import KiteConnect, KiteTicker
import xlwings as xw

# Configure logging
logging.basicConfig(level=logging.INFO)

# Global variables for dynamic configuration
api_key = None
access_token = None
kite = None
kws = None
tokens = []
instrument_mapping = {}
initial_ltp = {}  # Store initial LTP values
excel_app = None
excel_workbook = None
excel_worksheet = None
excel_file_path = None
excel_update_queue = queue.Queue()
update_lock = threading.Lock()

def load_credentials():
    """Load credentials dynamically from environment"""
    global api_key, access_token, kite

    # Load environment variables
    load_dotenv()

    api_key = os.getenv('API_KEY')
    if not api_key:
        logging.error("API_KEY not found in environment variables")
        return False

    # Load access token from file
    access_token = load_access_token_from_file()
    if not access_token:
        logging.error("Access token not available. Please run access_token.py first.")
        return False

    # Initialize KiteConnect
    kite = KiteConnect(api_key=api_key)
    kite.set_access_token(access_token)

    logging.info("✅ Credentials loaded successfully")
    return True

def load_access_token_from_file():
    """Load access token from session file dynamically"""
    try:
        session_file_name = f"{api_key}.json"
        if os.path.exists(session_file_name):
            with open(session_file_name, 'r') as session_file:
                session_data = json.load(session_file)
                token = session_data.get('access_token')
                if token:
                    logging.info(f"✅ Access token loaded from {session_file_name}")
                    return token

        logging.warning("⚠️ No valid access token found")
        return None

    except Exception as error:
        logging.error(f"❌ Error loading access token: {error}")
        return None
def initialize_instruments():
    """Initialize CRUDE OIL and NATURAL GAS futures instruments dynamically"""
    global tokens, instrument_mapping

    try:
        # Get all instruments dynamically
        all_instruments = kite.instruments()

        # Find CRUDE OIL and NATURAL GAS futures instruments dynamically
        target_symbols = ['CRUDEOIL', 'NATURALGAS']

        for instrument in all_instruments:
            if (instrument['segment'] == 'MCX-FUT' and
                instrument['instrument_type'] == 'FUT'):

                token = instrument['instrument_token']
                symbol = instrument['tradingsymbol']

                # Only get the latest expiry for each commodity
                base_symbol = None
                if symbol.startswith('CRUDEOIL') and not symbol.startswith('CRUDEOILM'):
                    base_symbol = 'CRUDEOIL'
                elif symbol.startswith('NATURALGAS') and not symbol.startswith('NATGASMINI'):
                    base_symbol = 'NATURALGAS'

                if base_symbol:
                    # Check if we already have this base symbol
                    existing_symbol = None
                    for existing_token, existing_name in instrument_mapping.items():
                        if existing_name.startswith(base_symbol):
                            existing_symbol = existing_name
                            break

                    # If no existing symbol or this one has an earlier expiry (nearest month), use this one
                    if not existing_symbol or symbol < existing_symbol:
                        # Remove existing if present
                        if existing_symbol:
                            for existing_token, existing_name in list(instrument_mapping.items()):
                                if existing_name == existing_symbol:
                                    tokens.remove(existing_token)
                                    del instrument_mapping[existing_token]
                                    break

                        tokens.append(token)
                        instrument_mapping[token] = symbol

                        logging.info(f"📊 Added instrument: {symbol} - Token: {token}")

        if len(tokens) >= 2:
            logging.info(f"✅ Successfully initialized {len(tokens)} instruments")
            return True
        else:
            logging.error("❌ Could not find required instruments")
            return False

    except Exception as error:
        logging.error(f"❌ Error initializing instruments: {error}")
        return False
def setup_excel_workbook():
    """Setup Excel workbook with persistent filename and single sheet"""
    global excel_app, excel_workbook, excel_worksheet, excel_file_path

    try:
        logging.info("🔄 Setting up Excel workbook...")

        # Set dynamic file path
        excel_file_path = os.path.join(os.getcwd(), "live_ltp_tracker.xlsx")

        # Kill any existing Excel processes to ensure clean start
        os.system("taskkill /f /im excel.exe >nul 2>&1")
        time.sleep(2)

        # Create xlwings app
        excel_app = xw.App(visible=True, add_book=False)

        # Check if file exists
        if os.path.exists(excel_file_path):
            logging.info(f"📁 Opening existing Excel file: {excel_file_path}")
            excel_workbook = excel_app.books.open(excel_file_path)
        else:
            logging.info(f"📁 Creating new Excel file: {excel_file_path}")
            excel_workbook = excel_app.books.add()
            excel_workbook.save(excel_file_path)

        # Remove all existing sheets
        for sheet in excel_workbook.sheets:
            if len(excel_workbook.sheets) > 1:  # Keep at least one sheet
                sheet.delete()

        # Create or get the live data sheet
        if len(excel_workbook.sheets) == 0:
            excel_worksheet = excel_workbook.sheets.add("Live_LTP_Data")
        else:
            excel_worksheet = excel_workbook.sheets[0]
            excel_worksheet.name = "Live_LTP_Data"

        # Clear existing content
        excel_worksheet.clear()

        # Setup headers dynamically with new columns
        headers = ["Instrument", "LTP", "Initial LTP", "LTP Diff", "Delta", "Signal Value", "CE Coefficient", "PE Coefficient", "Last Updated"]
        for i, header in enumerate(headers, 1):
            excel_worksheet.range(f'{chr(64+i)}1').value = header

        # Format headers
        header_range = excel_worksheet.range('A1:I1')
        header_range.color = (0, 112, 192)  # Blue background
        header_range.font.color = (255, 255, 255)  # White text
        header_range.font.bold = True

        # Setup data rows dynamically based on instrument mapping
        row = 2
        for token, symbol in instrument_mapping.items():
            if symbol.startswith('CRUDEOIL'):
                display_name = "CRUDE OIL"
                delta_value = 5  # Delta for crude oil
            elif symbol.startswith('NATURALGAS'):
                display_name = "NATURAL GAS"
                delta_value = 2  # Delta for natural gas
            else:
                display_name = symbol
                delta_value = 1

            excel_worksheet.range(f'A{row}').value = display_name
            excel_worksheet.range(f'E{row}').value = delta_value

            # Set default CE and PE coefficients (can be modified later)
            excel_worksheet.range(f'G{row}').value = 1.0  # CE Coefficient default
            excel_worksheet.range(f'H{row}').value = 1.0  # PE Coefficient default

            row += 1

        # Auto-fit columns
        excel_worksheet.autofit()

        logging.info("✅ Excel workbook setup completed successfully")
        return True

    except Exception as error:
        logging.error(f"❌ Error setting up Excel workbook: {error}")
        return False
# Callback for tick reception - following official pattern
def on_ticks(ws, ticks):
    """Handle incoming market tick data"""
    try:
        with update_lock:
            for tick in ticks:
                instrument_token = tick.get('instrument_token')
                last_price = tick.get('last_price', 0)

                if instrument_token in instrument_mapping:
                    symbol = instrument_mapping[instrument_token]

                    # Capture initial LTP if not already captured
                    if instrument_token not in initial_ltp:
                        initial_ltp[instrument_token] = last_price
                        logging.info(f"📊 Initial LTP captured for {symbol}: ₹{last_price}")

                    # Calculate LTP difference
                    ltp_diff = last_price - initial_ltp[instrument_token]

                    # Get delta value based on symbol
                    if symbol.startswith('CRUDEOIL'):
                        delta_value = 5  # Delta for crude oil
                    elif symbol.startswith('NATURALGAS'):
                        delta_value = 2  # Delta for natural gas
                    else:
                        delta_value = 1

                    # Get CE and PE coefficients from Excel (default to 1.0 if not set)
                    ce_coefficient = 1.0  # Default CE coefficient
                    pe_coefficient = 1.0  # Default PE coefficient

                    # Calculate Signal Value based on LTP Diff direction
                    if ltp_diff >= 0:
                        # Positive movement: use CE coefficient
                        signal_value = ltp_diff - (delta_value * ce_coefficient)
                        signal_type = "CE"
                    else:
                        # Negative movement: use PE coefficient
                        signal_value = ltp_diff - (delta_value * pe_coefficient * -1)  # Make delta negative for puts
                        signal_type = "PE"

                    # Queue Excel update with new data
                    update_data = {
                        'symbol': symbol,
                        'ltp': last_price,
                        'initial_ltp': initial_ltp[instrument_token],
                        'ltp_diff': ltp_diff,
                        'delta': delta_value,
                        'signal_value': signal_value,
                        'signal_type': signal_type,
                        'ce_coefficient': ce_coefficient,
                        'pe_coefficient': pe_coefficient,
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }

                    excel_update_queue.put(update_data)
                    logging.info(f"📈 {symbol}: ₹{last_price} (Diff: {ltp_diff:+.2f}) [{signal_type} Signal: {signal_value:+.2f}]")

    except Exception as error:
        logging.error(f"❌ Error processing tick data: {error}")

# Callback for successful connection - following official pattern
def on_connect(ws, response):
    """Handle WebSocket connection open event"""
    logging.info("Successfully connected. Response: {}".format(response))

    # Subscribe to instruments
    ws.subscribe(tokens)
    ws.set_mode(ws.MODE_FULL, tokens)
    logging.info("Subscribe to tokens in Full mode: {}".format(tokens))

# Callback when current connection is closed - following official pattern
def on_close(ws, code, reason):
    """Handle WebSocket connection close event"""
    logging.info("Connection closed: {code} - {reason}".format(code=code, reason=reason))

# Callback when connection closed with error - following official pattern
def on_error(ws, code, reason):
    """Handle WebSocket connection error"""
    logging.info("Connection error: {code} - {reason}".format(code=code, reason=reason))

# Callback when reconnect is on progress - following official pattern
def on_reconnect(ws, attempts_count):
    """Handle WebSocket reconnection"""
    logging.info("Reconnecting: {}".format(attempts_count))

# Callback when all reconnect failed - following official pattern
def on_noreconnect(ws):
    """Handle WebSocket reconnection failure"""
    logging.info("Reconnect failed.")

def reconnect_excel():
    """Reconnect to Excel if connection is lost"""
    global excel_app, excel_workbook, excel_worksheet

    try:
        logging.info("🔄 Attempting to reconnect to Excel...")

        # Kill any existing Excel processes
        os.system("taskkill /f /im excel.exe >nul 2>&1")
        time.sleep(3)

        # Recreate Excel connection
        excel_app = xw.App(visible=True, add_book=False)

        # Reopen the workbook
        if os.path.exists(excel_file_path):
            excel_workbook = excel_app.books.open(excel_file_path)
            excel_worksheet = excel_workbook.sheets["Live_LTP_Data"]
            logging.info("✅ Successfully reconnected to Excel")
            return True
        else:
            logging.error("❌ Excel file not found for reconnection")
            return False

    except Exception as error:
        logging.error(f"❌ Error reconnecting to Excel: {error}")
        return False

def update_excel_data():
    """Update Excel with queued data with improved error handling"""
    global excel_app, excel_workbook, excel_worksheet

    try:
        # Check if Excel is still responsive
        if excel_app is None or excel_workbook is None or excel_worksheet is None:
            if not reconnect_excel():
                return

        # Process updates in batches to reduce Excel load
        updates_processed = 0
        max_updates_per_cycle = 5  # Limit updates per cycle

        while not excel_update_queue.empty() and updates_processed < max_updates_per_cycle:
            update_data = excel_update_queue.get()
            symbol = update_data['symbol']
            ltp = update_data['ltp']
            initial_ltp_value = update_data['initial_ltp']
            ltp_diff = update_data['ltp_diff']
            delta_value = update_data['delta']
            signal_value = update_data['signal_value']
            signal_type = update_data['signal_type']
            ce_coefficient = update_data['ce_coefficient']
            pe_coefficient = update_data['pe_coefficient']
            timestamp = update_data['timestamp']

            # Determine row based on symbol dynamically
            row = None
            if symbol.startswith('CRUDEOIL'):
                row = 2
            elif symbol.startswith('NATURALGAS'):
                row = 3

            if row:
                try:
                    # Read current CE and PE coefficients from Excel (user can modify these)
                    try:
                        current_ce_coeff = excel_worksheet.range(f'G{row}').value or 1.0
                        current_pe_coeff = excel_worksheet.range(f'H{row}').value or 1.0
                    except:
                        current_ce_coeff = 1.0
                        current_pe_coeff = 1.0

                    # Recalculate signal value with current coefficients from Excel
                    if ltp_diff >= 0:
                        # Positive movement: use CE coefficient
                        recalculated_signal_value = ltp_diff - (delta_value * current_ce_coeff)
                        current_signal_type = "CE"
                    else:
                        # Negative movement: use PE coefficient
                        recalculated_signal_value = ltp_diff - (delta_value * current_pe_coeff * -1)
                        current_signal_type = "PE"

                    # Update Excel cells with new columns
                    excel_worksheet.range(f'B{row}').value = ltp  # Current LTP
                    excel_worksheet.range(f'C{row}').value = initial_ltp_value  # Initial LTP
                    excel_worksheet.range(f'D{row}').value = ltp_diff  # LTP Diff
                    # Delta (E) is set during setup and doesn't need updates
                    excel_worksheet.range(f'F{row}').value = recalculated_signal_value  # Signal Value
                    # CE Coefficient (G), PE Coefficient (H) can be modified by user
                    excel_worksheet.range(f'I{row}').value = timestamp  # Last Updated

                    # Color coding for LTP Diff
                    diff_cell = excel_worksheet.range(f'D{row}')
                    if ltp_diff > 0:
                        diff_cell.color = (144, 238, 144)  # Light green for positive
                    elif ltp_diff < 0:
                        diff_cell.color = (255, 182, 193)  # Light red for negative
                    else:
                        diff_cell.color = (255, 255, 255)  # White for zero

                    # Color coding for Signal Value with enhanced logic
                    signal_cell = excel_worksheet.range(f'F{row}')
                    if recalculated_signal_value > 0:
                        # Positive signal - potential entry signal
                        if current_signal_type == "CE":
                            signal_cell.color = (0, 255, 0)  # Bright green for CE signal
                        else:
                            signal_cell.color = (144, 238, 144)  # Light green for PE signal
                    elif recalculated_signal_value < 0:
                        # Negative signal - no entry
                        signal_cell.color = (255, 182, 193)  # Light red for negative
                    else:
                        # Zero signal - neutral
                        signal_cell.color = (255, 255, 255)  # White for zero

                    updates_processed += 1

                except Exception as cell_error:
                    logging.warning(f"⚠️ Error updating specific cell: {cell_error}")
                    # Try to reconnect on cell-level errors
                    if "RPC server" in str(cell_error):
                        if reconnect_excel():
                            continue  # Retry this update
                        else:
                            break  # Stop processing if reconnection fails

    except Exception as error:
        logging.error(f"❌ Error updating Excel: {error}")
        # Try to reconnect if RPC error
        if "RPC server" in str(error):
            reconnect_excel()
def main():
    """Main function following official KiteTicker pattern"""
    global kws

    try:
        # Load credentials dynamically
        if not load_credentials():
            logging.error("❌ Failed to load credentials. Exiting.")
            return

        # Initialize instruments dynamically
        if not initialize_instruments():
            logging.error("❌ Failed to initialize instruments. Exiting.")
            return

        # Setup Excel workbook
        if not setup_excel_workbook():
            logging.error("❌ Failed to setup Excel workbook. Exiting.")
            return

        # Initialize KiteTicker - following official pattern
        kws = KiteTicker(api_key, access_token)

        # Assign the callbacks - following official pattern
        kws.on_ticks = on_ticks
        kws.on_close = on_close
        kws.on_error = on_error
        kws.on_connect = on_connect
        kws.on_reconnect = on_reconnect
        kws.on_noreconnect = on_noreconnect

        # Start WebSocket connection in threaded mode - following official pattern
        kws.connect(threaded=True)

        # Block main thread and process Excel updates - following official pattern
        logging.info("This is main thread. Processing Excel updates every 1 second.")

        while True:
            try:
                # Process Excel updates
                update_excel_data()

                # Check connection status
                if kws.is_connected():
                    logging.debug("WebSocket is connected")
                else:
                    logging.warning("WebSocket is not connected")

                time.sleep(1.0)  # Update Excel every 1 second (reduced frequency)

            except KeyboardInterrupt:
                logging.info("🛑 Keyboard interrupt received. Stopping application...")
                break
            except Exception as loop_error:
                logging.error(f"❌ Error in main loop: {loop_error}")
                time.sleep(5)  # Wait before retrying

    except Exception as main_error:
        logging.error(f"❌ Critical error in main function: {main_error}")

    finally:
        # Cleanup
        try:
            if kws:
                kws.close()
                logging.info("✅ WebSocket connection closed")

            if excel_app:
                try:
                    if excel_workbook:
                        excel_workbook.save()
                        logging.info("✅ Excel workbook saved")
                    excel_app.quit()
                    logging.info("✅ Excel application closed")
                except Exception as excel_cleanup_error:
                    logging.warning(f"⚠️ Error closing Excel: {excel_cleanup_error}")
                    # Force kill Excel if normal close fails
                    os.system("taskkill /f /im excel.exe >nul 2>&1")

            logging.info("✅ Application shutdown completed successfully")
        except Exception as cleanup_error:
            logging.error(f"❌ Error during cleanup: {cleanup_error}")

if __name__ == "__main__":
    main()
