


# Live LTP Data Streaming to CSV (Linux Compatible)




This project streams live Last Traded Price (LTP) data for NIFTY 50 and NIFTY BANK indices from Zerodha Kite Connect API to CSV files using websockets in threaded mode. **Linux compatible** - no Excel dependency required!

## Features

- **Real-time Data**: Uses KiteTicker websocket for live market data
- **Threaded Operation**: Websocket runs in a separate thread, allowing main thread to continue
- **CSV Output**: Automatically saves data to CSV file (works on Linux/Windows/macOS)
- **Two Instruments**: Tracks NIFTY 50 and NIFTY BANK indices
- **Comprehensive Data**: Stores timestamp, instrument name, token, LTP, volume, change, OHLC data
- **Data Viewer**: Interactive tool to view, analyze, and export data
- **Auto Token Loading**: Automatically loads access token from JSON file

## Prerequisites

- Python 3.7+
- **No Excel required** - works on Linux!
- Zerodha Kite Connect API credentials
- Required Python packages (already installed):
  - kiteconnect
  - pandas
  - python-dotenv
  - pyotp

## Setup Instructions

### Step 1: Environment Variables
Your `.env` file should contain:
```
API_KEY=your_api_key
API_SECRET=your_api_secret
LOGIN_ID=your_zerodha_user_id
PASSWORD=your_zerodha_password
TOTP_KEY=your_totp_secret_key
```

### Step 2: Access Token (Automatic)
The application automatically loads the access token from your JSON file:
- File: `/home/<USER>/workspace/quant/w26lp3vw7neghs2h.json`
- No manual token generation needed!

### Step 3: Run the Live Data Streamer
```bash
python main.py
```

### Step 4: View Your Data
Use the interactive data viewer:
```bash
python view_data.py
```

Options available:
1. **View Live Data** - Real-time auto-refreshing display
2. **Export to Excel** - Convert CSV to Excel format
3. **Show Summary** - Statistics and data overview

## How It Works

### 1. Instrument Discovery
- Fetches all available instruments from Kite Connect
- Filters for INDICES segment
- Selects NIFTY 50 and NIFTY BANK
- Extracts instrument tokens for websocket subscription

### 2. Websocket Connection (Threaded)
- Initializes KiteTicker with API key and access token
- Connects to websocket in threaded mode
- Subscribes to instrument tokens in FULL mode
- Receives real-time tick data

### 3. CSV Integration
- Creates/opens `live_ltp_data.csv` file
- Sets up CSV with headers
- Updates CSV in real-time with each tick
- Thread-safe CSV operations using locks

### 4. Data Structure
Each row in CSV contains:
- **Timestamp**: When the data was received
- **Instrument**: Trading symbol (NIFTY 50 / NIFTY BANK)
- **Token**: Instrument token
- **LTP**: Last Traded Price
- **Volume**: Trading volume
- **Change**: Price change
- **Open/High/Low/Close**: OHLC data

## File Structure

```
quant/
├── main.py                        # Clean optimized main application
├── config.py                      # Trading strategy configuration
├── instrument_manager.py          # Instrument data management
├── order_manager.py              # Order placement and management
├── strategy_tracker.py           # Options trading strategy logic
├── excel_dashboard.py            # Live Excel dashboard
├── access_token.py               # Authentication management
├── requirements.txt              # Project dependencies
├── strategy.md                   # Strategy documentation
├── CleanStrategy_Dashboard.xlsx  # Excel dashboard file
├── OPTIMIZATION_SUMMARY.md       # Optimization documentation
└── README.md                     # This file
```

## Key Classes and Functions

### CSVUpdater Class
- `setup_csv()`: Initializes CSV file with headers
- `update_csv(tick_data)`: Thread-safe CSV updates
- `get_latest_data()`: Retrieves recent data as DataFrame

### Websocket Callbacks
- `on_connect()`: Handles successful connection
- `on_ticks()`: Processes incoming tick data
- `on_error()`: Handles connection errors
- `on_reconnect()`: Manages reconnection attempts

## Monitoring

The application provides real-time logging:
- Connection status
- Tick data reception
- Excel updates
- Error handling

## Stopping the Application

Press `Ctrl+C` to stop the application. It will:
- Close websocket connection
- Save CSV file automatically
- Clean up resources

## Troubleshooting

### Common Issues

1. **Access Token Error**: Run `simple_token_generator.py` to generate a new token
2. **Excel Permission Error**: Close Excel file if it's open
3. **Connection Issues**: Check internet connection and API credentials
4. **Missing Data**: Ensure market is open during trading hours

### Logs
Check console output for detailed logging information about:
- Connection status
- Data reception
- Excel updates
- Error messages

## Market Hours
The websocket will only receive data during market hours:
- **Equity**: 9:15 AM to 3:30 PM (IST)
- **Indices**: 9:15 AM to 3:30 PM (IST)

## Notes

- The application runs continuously until manually stopped
- Excel file is updated in real-time with each tick
- Data is appended to the Excel file (historical data is preserved)
- Thread-safe operations ensure data integrity
- Automatic reconnection handling for network issues
